import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/app/lib/mongodb';

// Razorpay initialization
import <PERSON><PERSON>pay from 'razorpay';

// Get Razorpay keys from environment variables
const RAZORPAY_KEY_ID = process.env.RAZORPAY_KEY_ID;
const RAZORPAY_KEY_SECRET = process.env.RAZORPAY_KEY_SECRET;

// Only log in development mode
if (process.env.NODE_ENV === 'development') {
  console.log('RAZORPAY_KEY_ID:', RAZORPAY_KEY_ID ? 'Exists' : 'Missing');
  console.log('RAZORPAY_KEY_SECRET:', RAZORPAY_KEY_SECRET ? 'Exists' : 'Missing');
}

if (!RAZORPAY_KEY_ID || !RAZORPAY_KEY_SECRET) {
  console.error('Razorpay keys are missing in environment variables');
}

// Initialize Razorpay with fallback for testing
let razorpay;
try {
  razorpay = new Razorpay({
    key_id: RAZOR<PERSON>Y_KEY_ID || 'rzp_test_placeholder',
    key_secret: RAZORPAY_KEY_SECRET || 'test_secret_placeholder',
  });
} catch (error) {
  console.error('Failed to initialize Razorpay:', error);
  // Create a mock Razorpay instance for testing
  razorpay = {
    orders: {
      create: async () => ({ id: 'mock_order_' + Date.now() })
    }
  };
}

export async function POST(request: Request) {
  try {
    const { amount, currency, receipt, notes } = await request.json();

    // Validate input
    if (!amount || !currency || !receipt) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create a mock order for testing purposes
    // This simulates a successful Razorpay order creation
    const mockOrderId = `order_${Date.now()}${Math.floor(Math.random() * 1000)}`;

    // Debug log to help troubleshoot amount issues
    console.log('Create Order - Original amount received:', amount, typeof amount);

    // Razorpay expects amount in paise (smallest currency unit)
    // Convert amount to paise (1 rupee = 100 paise)
    const amountInPaise = Math.round(amount * 100);

    console.log('Create Order - Amount in paise:', amountInPaise);

    const order = {
      id: mockOrderId,
      entity: "order",
      amount: amountInPaise,
      amount_paid: 0,
      amount_due: amountInPaise,
      currency: currency,
      receipt: receipt,
      status: "created",
      attempts: 0,
      notes: notes,
      created_at: Math.floor(Date.now() / 1000)
    };

    console.log('Mock order created:', order);

    // Store order in database
    try {
      const { db } = await connectToDatabase({ timeoutMs: 10000 });
      await db.collection('orders').insertOne({
        orderId: order.id,
        amount: amount, // Store original amount in rupees
        amountInPaise: order.amount, // Also store amount in paise for reference
        currency: order.currency,
        receipt: order.receipt,
        notes: order.notes,
        status: order.status,
        createdAt: new Date(),
      });
      console.log('Order saved to database');
    } catch (dbError) {
      console.error('Database error:', dbError);
      // Continue even if database save fails
    }

    return NextResponse.json({ order }, { status: 200 });
  } catch (error) {
    console.error('Create order error:', error);
    return NextResponse.json(
      { message: 'Failed to create order' },
      { status: 500 }
    );
  }
}
