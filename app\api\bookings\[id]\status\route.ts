import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/app/lib/mongodb";
import { ObjectId } from "mongodb";
import { verifyToken } from "@/app/lib/auth";
import { logger } from "@/app/config/logger";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Booking ID is required" },
        { status: 400 }
      );
    }

    // Verify authentication
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, message: "Authorization token required" },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json(
        { success: false, message: "Invalid or expired token" },
        { status: 401 }
      );
    }

    // Connect to database
    const { db } = await connectToDatabase();

    // Find booking by different ID formats
    let booking = null;

    // Try ObjectId format first
    if (ObjectId.isValid(id)) {
      booking = await db.collection("bookings").findOne({ _id: new ObjectId(id) });
    }

    // Try bookingId field
    if (!booking) {
      booking = await db.collection("bookings").findOne({ bookingId: id });
    }

    // Try id field
    if (!booking) {
      booking = await db.collection("bookings").findOne({ id: id });
    }

    if (!booking) {
      logger.warn("Booking not found", { bookingId: id, userId: decoded.userId });
      return NextResponse.json(
        { success: false, message: "Booking not found" },
        { status: 404 }
      );
    }

    // Check if user owns this booking
    const bookingUserId = booking.userId?.toString();
    const bookingUserEmail = booking.customerEmail;
    const requestUserId = decoded.userId?.toString();
    const requestUserEmail = decoded.email;

    // Allow access if either userId matches or email matches
    const hasAccess = (bookingUserId && bookingUserId === requestUserId) ||
                     (bookingUserEmail && bookingUserEmail === requestUserEmail);

    if (!hasAccess) {
      logger.warn("Unauthorized booking access attempt", {
        bookingId: id,
        bookingUserId,
        bookingUserEmail,
        requestUserId,
        requestUserEmail
      });
      return NextResponse.json(
        { success: false, message: "Unauthorized access to booking" },
        { status: 403 }
      );
    }

    // Determine current status
    let currentStatus = booking.status || booking.bookingStatus || 'pending_technician';
    let statusMessage = '';

    // Map internal statuses to user-friendly statuses
    switch (currentStatus) {
      case 'pending_technician':
      case 'pending':
        currentStatus = 'processing';
        statusMessage = 'Looking for available technicians...';
        break;
      case 'confirmed':
      case 'assigned':
        currentStatus = 'confirmed';
        statusMessage = booking.technicianName
          ? `Technician ${booking.technicianName} has accepted your booking`
          : 'A technician has been assigned to your booking';
        break;
      case 'rejected':
        currentStatus = 'rejected';
        statusMessage = 'Technician was unable to accept your booking';
        break;
      case 'no_technician_available':
        currentStatus = 'no_technician';
        statusMessage = 'No technicians are available in your area right now';
        break;
      case 'in_progress':
        currentStatus = 'confirmed';
        statusMessage = 'Service is in progress';
        break;
      case 'completed':
        currentStatus = 'confirmed';
        statusMessage = 'Service has been completed';
        break;
      case 'cancelled':
        currentStatus = 'rejected';
        statusMessage = 'Booking has been cancelled';
        break;
      default:
        currentStatus = 'processing';
        statusMessage = 'Processing your booking...';
    }

    // Check if booking has expired (more than 5 minutes old and still processing)
    const bookingAge = Date.now() - new Date(booking.createdAt).getTime();
    const fiveMinutes = 5 * 60 * 1000;

    if (currentStatus === 'processing' && bookingAge > fiveMinutes) {
      currentStatus = 'expired';
      statusMessage = 'Booking has expired. Please try again.';

      // Update booking status in database
      await db.collection("bookings").updateOne(
        { _id: booking._id },
        {
          $set: {
            status: 'expired',
            statusMessage: statusMessage,
            updatedAt: new Date()
          }
        }
      );
    }

    // Prepare response
    const response = {
      success: true,
      booking: {
        id: booking._id.toString(),
        bookingId: booking.bookingId || booking.id,
        status: currentStatus,
        statusMessage: statusMessage,
        service: booking.service || booking.serviceName,
        amount: booking.amount,
        customerName: booking.customerName,
        customerAddress: booking.customerAddress || booking.address,
        bookingDate: booking.bookingDate || booking.scheduledDate,
        bookingTime: booking.bookingTime || booking.scheduledTime,
        paymentMethod: booking.paymentMethod,
        technicianId: booking.technicianId,
        technicianName: booking.technicianName,
        technicianPhone: booking.technicianPhone,
        assignedAt: booking.assignedAt,
        createdAt: booking.createdAt,
        updatedAt: booking.updatedAt || booking.createdAt
      }
    };

    logger.info("Booking status retrieved", {
      bookingId: id,
      userId: decoded.userId,
      status: currentStatus
    });

    return NextResponse.json(response);

  } catch (error) {
    logger.error("Error fetching booking status", {
      error: error instanceof Error ? error.message : "Unknown error",
      bookingId: id || "unknown"
    });

    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
