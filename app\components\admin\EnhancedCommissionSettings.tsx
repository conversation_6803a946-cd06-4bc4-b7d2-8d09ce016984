"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>aH<PERSON>ory,
  FaToggleOn,
  Fa<PERSON><PERSON>gle<PERSON>ff,
  FaInfoCircle,
  FaExclamationTriangle,
  FaChartLine,
  FaEdit,
  FaCheck,
  FaTimes,
} from "react-icons/fa";
import { toast } from "react-hot-toast";

interface CategoryCommission {
  categoryId: string;
  categoryName: string;
  commissionRate: number;
  isActive: boolean;
  updatedAt: string;
  updatedBy: string;
}

interface CommissionSettings {
  type: "category-based" | "global";
  globalRate?: number;
  categoryRates?: CategoryCommission[];
  defaultRate: number;
  createdAt: string;
  updatedAt: string;
  updatedBy: string;
}

interface ServiceCategory {
  _id: string;
  name: string;
  slug: string;
  description?: string;
  isActive: boolean;
  order: number;
}

export default function EnhancedCommissionSettings() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<CommissionSettings | null>(null);
  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [commissionType, setCommissionType] = useState<"global" | "category-based">("global");
  const [globalRate, setGlobalRate] = useState(30);
  const [categoryRates, setCategoryRates] = useState<CategoryCommission[]>([]);
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [showHistory, setShowHistory] = useState(false);
  const [history, setHistory] = useState<any[]>([]);

  useEffect(() => {
    fetchCommissionSettings();
  }, []);

  const fetchCommissionSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/payments/commission", {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch commission settings: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setSettings(data.settings);
        setCategories(data.categories);
        setCommissionType(data.settings.type);
        setGlobalRate(data.settings.globalRate || 30);
        setCategoryRates(data.settings.categoryRates || []);
      } else {
        throw new Error(data.message || "Failed to fetch commission settings");
      }
    } catch (error) {
      console.error("Error fetching commission settings:", error);
      toast.error("Failed to load commission settings");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (commissionType === "global" && (globalRate < 0 || globalRate > 100)) {
      toast.error("Global commission rate must be between 0 and 100");
      return;
    }

    if (commissionType === "category-based") {
      const invalidRates = categoryRates.filter(rate => rate.commissionRate < 0 || rate.commissionRate > 100);
      if (invalidRates.length > 0) {
        toast.error("All commission rates must be between 0 and 100");
        return;
      }
    }

    try {
      setSaving(true);

      const response = await fetch("/api/admin/payments/commission", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({
          type: commissionType,
          globalRate: commissionType === "global" ? globalRate : undefined,
          categoryRates: commissionType === "category-based" ? categoryRates : undefined,
          defaultRate: 30,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update commission settings: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        toast.success("Commission settings updated successfully");
        setSettings(data.settings);
        setEditingCategory(null);
      } else {
        throw new Error(data.message || "Failed to update commission settings");
      }
    } catch (error) {
      console.error("Error updating commission settings:", error);
      toast.error("Failed to update commission settings");
    } finally {
      setSaving(false);
    }
  };

  const handleCategoryRateChange = (categoryId: string, newRate: number) => {
    setCategoryRates(prev =>
      prev.map(rate =>
        rate.categoryId === categoryId
          ? { ...rate, commissionRate: newRate }
          : rate
      )
    );
  };

  const toggleCategoryActive = (categoryId: string) => {
    setCategoryRates(prev =>
      prev.map(rate =>
        rate.categoryId === categoryId
          ? { ...rate, isActive: !rate.isActive }
          : rate
      )
    );
  };

  const handleTypeChange = (newType: "global" | "category-based") => {
    setCommissionType(newType);
    
    if (newType === "category-based" && categoryRates.length === 0) {
      // Initialize category rates with current global rate
      const initialRates = categories.map(category => ({
        categoryId: category._id,
        categoryName: category.name,
        commissionRate: globalRate,
        isActive: true,
        updatedAt: new Date().toISOString(),
        updatedBy: "admin"
      }));
      setCategoryRates(initialRates);
    }
  };

  const fetchHistory = async () => {
    try {
      const response = await fetch("/api/admin/payments/commission", {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setHistory(data.history || []);
      }
    } catch (error) {
      console.error("Error fetching history:", error);
    }
  };

  const toggleHistory = () => {
    setShowHistory(!showHistory);
    if (!showHistory && history.length === 0) {
      fetchHistory();
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <FaSpinner className="animate-spin h-8 w-8 text-blue-500" />
        <span className="ml-2 text-gray-600">Loading commission settings...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Commission Type Selection */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Commission Structure</h3>
          <p className="mt-1 text-sm text-gray-500">
            Choose between global or category-based commission rates
          </p>
        </div>
        
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                commissionType === "global"
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200 hover:border-gray-300"
              }`}
              onClick={() => handleTypeChange("global")}
            >
              <div className="flex items-center">
                <FaPercent className="h-5 w-5 text-blue-500 mr-3" />
                <div>
                  <h4 className="font-medium text-gray-900">Global Rate</h4>
                  <p className="text-sm text-gray-500">Single commission rate for all services</p>
                </div>
              </div>
            </div>

            <div
              className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                commissionType === "category-based"
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200 hover:border-gray-300"
              }`}
              onClick={() => handleTypeChange("category-based")}
            >
              <div className="flex items-center">
                <FaChartLine className="h-5 w-5 text-green-500 mr-3" />
                <div>
                  <h4 className="font-medium text-gray-900">Category-Based</h4>
                  <p className="text-sm text-gray-500">Different rates for each service category</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Commission Settings Form */}
      <form onSubmit={handleSubmit} className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Commission Rates</h3>
            <p className="mt-1 text-sm text-gray-500">
              {commissionType === "global" 
                ? "Set a single commission rate for all services"
                : "Configure commission rates for each service category"
              }
            </p>
          </div>
          <button
            type="button"
            onClick={toggleHistory}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FaHistory className="mr-2 h-4 w-4" />
            History
          </button>
        </div>

        <div className="px-6 py-6">
          {commissionType === "global" ? (
            <div className="max-w-md">
              <label htmlFor="globalRate" className="block text-sm font-medium text-gray-700 mb-2">
                Global Commission Rate (%)
              </label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaPercent className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="number"
                  id="globalRate"
                  min="0"
                  max="100"
                  step="0.1"
                  value={globalRate}
                  onChange={(e) => setGlobalRate(parseFloat(e.target.value) || 0)}
                  className="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 pr-12 sm:text-sm border-gray-300 rounded-md"
                  placeholder="30"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">%</span>
                </div>
              </div>
              <p className="mt-2 text-sm text-gray-500">
                This rate will apply to all service categories
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {categoryRates.map((rate) => (
                <div key={rate.categoryId} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <h4 className="font-medium text-gray-900">{rate.categoryName}</h4>
                      <button
                        type="button"
                        onClick={() => toggleCategoryActive(rate.categoryId)}
                        className="flex items-center"
                      >
                        {rate.isActive ? (
                          <FaToggleOn className="h-5 w-5 text-green-500" />
                        ) : (
                          <FaToggleOff className="h-5 w-5 text-gray-400" />
                        )}
                      </button>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {editingCategory === rate.categoryId ? (
                        <div className="flex items-center space-x-2">
                          <div className="relative">
                            <input
                              type="number"
                              min="0"
                              max="100"
                              step="0.1"
                              value={rate.commissionRate}
                              onChange={(e) => handleCategoryRateChange(rate.categoryId, parseFloat(e.target.value) || 0)}
                              className="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                            />
                            <span className="absolute right-2 top-1 text-xs text-gray-500">%</span>
                          </div>
                          <button
                            type="button"
                            onClick={() => setEditingCategory(null)}
                            className="text-green-600 hover:text-green-700"
                          >
                            <FaCheck className="h-4 w-4" />
                          </button>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <span className="text-lg font-semibold text-gray-900">
                            {rate.commissionRate}%
                          </span>
                          <button
                            type="button"
                            onClick={() => setEditingCategory(rate.categoryId)}
                            className="text-blue-600 hover:text-blue-700"
                          >
                            <FaEdit className="h-4 w-4" />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {!rate.isActive && (
                    <div className="mt-2 flex items-center text-sm text-amber-600">
                      <FaExclamationTriangle className="h-4 w-4 mr-1" />
                      This category is disabled
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end">
          <button
            type="submit"
            disabled={saving}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {saving ? (
              <FaSpinner className="animate-spin mr-2 h-4 w-4" />
            ) : (
              <FaSave className="mr-2 h-4 w-4" />
            )}
            {saving ? "Saving..." : "Save Settings"}
          </button>
        </div>
      </form>

      {/* Commission History */}
      {showHistory && (
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Commission History</h3>
            <p className="mt-1 text-sm text-gray-500">Recent changes to commission settings</p>
          </div>
          
          <div className="px-6 py-4">
            {history.length === 0 ? (
              <p className="text-sm text-gray-500 italic">No commission changes recorded</p>
            ) : (
              <div className="space-y-3">
                {history.slice(0, 10).map((item, index) => (
                  <div key={index} className="border-l-4 border-blue-500 pl-4 py-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900">
                        Changed to {item.type} commission structure
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(item.updatedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Impact Analysis */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Commission Impact</h3>
          <p className="mt-1 text-sm text-gray-500">
            Understand how commission rates affect earnings and revenue
          </p>
        </div>
        
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-green-50 rounded-lg p-4 border border-green-100">
              <div className="flex items-center">
                <FaInfoCircle className="h-5 w-5 text-green-600 mr-2" />
                <h4 className="text-sm font-medium text-green-800">Platform Revenue</h4>
              </div>
              <p className="mt-1 text-xs text-green-600">
                Higher rates increase platform revenue but may reduce technician satisfaction
              </p>
            </div>
            
            <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
              <div className="flex items-center">
                <FaInfoCircle className="h-5 w-5 text-blue-600 mr-2" />
                <h4 className="text-sm font-medium text-blue-800">Technician Earnings</h4>
              </div>
              <p className="mt-1 text-xs text-blue-600">
                Lower rates increase technician earnings and may attract more skilled workers
              </p>
            </div>
            
            <div className="bg-purple-50 rounded-lg p-4 border border-purple-100">
              <div className="flex items-center">
                <FaInfoCircle className="h-5 w-5 text-purple-600 mr-2" />
                <h4 className="text-sm font-medium text-purple-800">Market Competitiveness</h4>
              </div>
              <p className="mt-1 text-xs text-purple-600">
                Category-based rates allow competitive pricing for different service types
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
