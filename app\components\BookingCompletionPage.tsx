"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FaCheckCircle, FaCalendarAlt, FaClock, FaMapMarkerAlt, FaPhone, FaEnvelope, FaHome, FaReceipt } from "react-icons/fa";
import Link from "next/link";

interface BookingDetails {
  bookingId: string;
  service: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  customerAddress: string;
  bookingDate: string;
  bookingTime: string;
  amount: number;
  paymentMethod: 'online' | 'cash';
  paymentId?: string;
  orderId?: string;
}

interface BookingCompletionPageProps {
  isOpen: boolean;
  bookingDetails: BookingDetails;
  onClose: () => void;
}

export default function BookingCompletionPage({ isOpen, bookingDetails, onClose }: BookingCompletionPageProps) {
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => setShowDetails(true), 1000);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          >
            {/* Success Header */}
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-8 text-center rounded-t-2xl">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              >
                <FaCheckCircle className="h-20 w-20 mx-auto mb-4" />
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="text-3xl font-bold mb-2"
              >
                Booking Received!
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="text-green-100 text-lg"
              >
                {bookingDetails.paymentMethod === 'cash'
                  ? 'Your service request has been received successfully'
                  : 'Payment successful! Your service request has been received'
                }
              </motion.p>
            </div>

            {/* Booking Details */}
            <div className="p-8">
              <AnimatePresence>
                {showDetails && (
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-6"
                  >
                    {/* Booking ID */}
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <div className="flex items-center">
                        <FaReceipt className="h-5 w-5 text-blue-600 mr-3" />
                        <div>
                          <p className="text-sm text-blue-600 font-medium">Booking ID</p>
                          <p className="text-lg font-bold text-blue-800">{bookingDetails.bookingId}</p>
                        </div>
                      </div>
                    </div>

                    {/* Service Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-gray-800 mb-3">Service Details</h3>
                        <div className="space-y-2">
                          <div className="flex items-center">
                            <FaCalendarAlt className="h-4 w-4 text-gray-500 mr-2" />
                            <span className="text-sm text-gray-600">Service:</span>
                            <span className="ml-2 font-medium">{bookingDetails.service}</span>
                          </div>
                          <div className="flex items-center">
                            <FaCalendarAlt className="h-4 w-4 text-gray-500 mr-2" />
                            <span className="text-sm text-gray-600">Date:</span>
                            <span className="ml-2 font-medium">{formatDate(bookingDetails.bookingDate)}</span>
                          </div>
                          <div className="flex items-center">
                            <FaClock className="h-4 w-4 text-gray-500 mr-2" />
                            <span className="text-sm text-gray-600">Time:</span>
                            <span className="ml-2 font-medium">{bookingDetails.bookingTime}</span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-gray-800 mb-3">Customer Details</h3>
                        <div className="space-y-2">
                          <div className="flex items-center">
                            <FaPhone className="h-4 w-4 text-gray-500 mr-2" />
                            <span className="text-sm text-gray-600">Name:</span>
                            <span className="ml-2 font-medium">{bookingDetails.customerName}</span>
                          </div>
                          <div className="flex items-center">
                            <FaPhone className="h-4 w-4 text-gray-500 mr-2" />
                            <span className="text-sm text-gray-600">Phone:</span>
                            <span className="ml-2 font-medium">{bookingDetails.customerPhone}</span>
                          </div>
                          <div className="flex items-center">
                            <FaEnvelope className="h-4 w-4 text-gray-500 mr-2" />
                            <span className="text-sm text-gray-600">Email:</span>
                            <span className="ml-2 font-medium text-xs">{bookingDetails.customerEmail}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Address */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="font-semibold text-gray-800 mb-2">Service Address</h3>
                      <div className="flex items-start">
                        <FaMapMarkerAlt className="h-4 w-4 text-gray-500 mr-2 mt-1" />
                        <span className="text-gray-700">{bookingDetails.customerAddress}</span>
                      </div>
                    </div>

                    {/* Payment Details */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="font-semibold text-gray-800 mb-3">Payment Details</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-600">Amount</p>
                          <p className="text-xl font-bold text-green-600">₹{bookingDetails.amount}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Payment Method</p>
                          <p className="font-medium capitalize">
                            {bookingDetails.paymentMethod === 'cash' ? 'Cash on Service' : 'Online Payment'}
                          </p>
                        </div>
                        {bookingDetails.paymentMethod === 'online' && bookingDetails.paymentId && (
                          <>
                            <div>
                              <p className="text-sm text-gray-600">Payment ID</p>
                              <p className="font-mono text-sm">{bookingDetails.paymentId}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Order ID</p>
                              <p className="font-mono text-sm">{bookingDetails.orderId}</p>
                            </div>
                          </>
                        )}
                      </div>
                    </div>

                    {/* Next Steps */}
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="font-semibold text-blue-800 mb-2">What's Next?</h3>
                      <ul className="text-blue-700 space-y-1 text-sm">
                        <li>• A confirmation email has been sent to your email address</li>
                        <li>• We are finding an available technician for your service</li>
                        <li>• You will receive a notification once a technician accepts your booking</li>
                        <li>• The technician will contact you to confirm the appointment time</li>
                        {bookingDetails.paymentMethod === 'cash' && (
                          <li>• Please keep the exact amount ready for payment after service completion</li>
                        )}
                        <li>• If no technician is available, we will notify you to reschedule</li>
                      </ul>
                    </div>

                    {/* Status Alert */}
                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-yellow-800">
                            Booking Status: Waiting for Technician
                          </h3>
                          <div className="mt-2 text-sm text-yellow-700">
                            <p>Your booking is currently being reviewed by available technicians. You will be notified once a technician accepts your service request.</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-4 pt-4">
                      <Link
                        href="/profile/orders"
                        className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors text-center font-medium"
                      >
                        View My Bookings
                      </Link>
                      <Link
                        href="/"
                        className="flex-1 bg-gray-200 text-gray-800 py-3 px-6 rounded-lg hover:bg-gray-300 transition-colors text-center font-medium flex items-center justify-center"
                      >
                        <FaHome className="mr-2" />
                        Back to Home
                      </Link>
                      <button
                        onClick={onClose}
                        className="flex-1 bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors font-medium"
                      >
                        Close
                      </button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
