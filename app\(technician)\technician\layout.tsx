"use client";

import { TechnicianJobProvider } from "@/app/contexts/TechnicianJobContext";
import TechnicianHeader from "@/app/components/technician/TechnicianHeader";

export default function TechnicianLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <TechnicianJobProvider>
      <div className="min-h-screen bg-gray-50">
        {/* Fixed Technician Header */}
        <div className="fixed top-0 left-0 right-0 z-50">
          <TechnicianHeader />
        </div>

        {/* Main Content with top padding to account for fixed header */}
        <div className="pt-16 w-full">
          <div className="container mx-auto px-4 py-6">
            {children}
          </div>
        </div>
      </div>
    </TechnicianJobProvider>
  );
}
