"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { toast } from "react-hot-toast";
import EnhancedCommissionSettings from "@/app/components/admin/EnhancedCommissionSettings";
import { FaArrowLeft, FaSpinner, FaPercent, FaChartLine, FaInfoCircle } from "react-icons/fa";

export default function AdminCommissionPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem("token");
    if (!token) {
      toast.error("Please log in to access this page");
      router.push("/admin/login");
      return;
    }

    // Simulate loading check
    const timer = setTimeout(() => {
      setLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <FaSpinner className="animate-spin h-8 w-8 text-blue-500 mx-auto mb-4" />
          <p className="text-gray-600">Loading commission settings...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
            <h3 className="text-lg font-medium text-red-800 mb-2">Error Loading Page</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Commission Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Configure commission rates and manage technician earnings
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <Link
            href="/admin/payments"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            aria-label="Back to payments"
          >
            <FaArrowLeft className="mr-2 -ml-1 h-5 w-5 text-gray-500" aria-hidden="true" />
            Back to Payments
          </Link>
          <Link
            href="/admin/payments/earnings"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            aria-label="View earnings overview"
          >
            <FaChartLine className="mr-2 -ml-1 h-5 w-5" aria-hidden="true" />
            Earnings Overview
          </Link>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FaPercent className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Commission Structure
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    Category-Based
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FaChartLine className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Active Categories
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    5 Categories
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FaInfoCircle className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Last Updated
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    Today
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Commission Settings Component */}
      <EnhancedCommissionSettings />

      {/* Help Section */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <FaInfoCircle className="h-5 w-5 text-blue-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Commission Management Tips
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc pl-5 space-y-1">
                <li>
                  <strong>Global Rate:</strong> Use when you want the same commission for all services. 
                  Simple to manage but less flexible.
                </li>
                <li>
                  <strong>Category-Based:</strong> Set different rates for each service type. 
                  More complex but allows optimized pricing strategies.
                </li>
                <li>
                  <strong>Rate Changes:</strong> All changes are logged and can be viewed in the history section. 
                  Changes apply to new bookings immediately.
                </li>
                <li>
                  <strong>Impact Analysis:</strong> Consider technician satisfaction and market competitiveness 
                  when setting rates.
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-8 bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
          <p className="mt-1 text-sm text-gray-500">
            Common commission management tasks
          </p>
        </div>
        
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link
              href="/admin/payments/payouts"
              className="flex items-center p-3 border border-gray-200 rounded hover:bg-gray-50 transition-colors"
            >
              <FaChartLine className="h-5 w-5 text-blue-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900">Manage Payouts</p>
                <p className="text-xs text-gray-600">Process technician payments</p>
              </div>
            </Link>

            <Link
              href="/admin/payments/earnings"
              className="flex items-center p-3 border border-gray-200 rounded hover:bg-gray-50 transition-colors"
            >
              <FaPercent className="h-5 w-5 text-green-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900">Earnings Overview</p>
                <p className="text-xs text-gray-600">View earnings analytics</p>
              </div>
            </Link>

            <Link
              href="/admin/reports"
              className="flex items-center p-3 border border-gray-200 rounded hover:bg-gray-50 transition-colors"
            >
              <FaChartLine className="h-5 w-5 text-purple-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900">Commission Reports</p>
                <p className="text-xs text-gray-600">Detailed analytics</p>
              </div>
            </Link>

            <Link
              href="/admin/technicians/performance"
              className="flex items-center p-3 border border-gray-200 rounded hover:bg-gray-50 transition-colors"
            >
              <FaInfoCircle className="h-5 w-5 text-orange-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900">Technician Performance</p>
                <p className="text-xs text-gray-600">View performance metrics</p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
