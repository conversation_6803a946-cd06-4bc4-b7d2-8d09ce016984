"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import DashboardOverview from "@/app/components/admin/DashboardOverview";
import OrdersManagement from "@/app/components/admin/OrdersManagement";
import DashboardAnalytics from "@/app/components/admin/DashboardAnalytics";
import BookingAlerts from "@/app/components/admin/BookingAlerts";
import { toast } from "react-hot-toast";

interface User {
  role: string;
  name?: string;
  email?: string;
  id?: string;
}

export default function AdminDashboard() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<"overview" | "analytics">("overview");
  const [isLoading, setIsLoading] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [user, setUser] = useState<User | null>(null);

  const checkAdminAuth = useCallback(async () => {
    try {
      setLoadingProgress(30);

      // Get user data from localStorage
      const userData = localStorage.getItem("user");
      if (!userData) {
        router.push("/admin/login");
        return;
      }

      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);

      // Verify admin role
      if (parsedUser.role !== "admin") {
        router.push("/login");
        return;
      }

      setLoadingProgress(70);

      // Verify token with server
      const response = await fetch("/api/admin/verify", {
        method: "GET",
        credentials: "include",
      });

      const data = await response.json();
      if (!data.success) {
        // Clear invalid authentication data
        localStorage.removeItem("token");
        localStorage.removeItem("user");
        router.push("/admin/login");
        return;
      }

      setLoadingProgress(100);
      toast.success("Dashboard loaded successfully!", { duration: 2000 });
      setIsLoading(false);
    } catch (error) {
      console.error("Error checking admin auth:", error);
      // Clear authentication data and redirect
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      router.push("/admin/login");
    }
  }, [router]);

  useEffect(() => {
    checkAdminAuth();

    // Show welcome message for fresh login
    const freshLogin = localStorage.getItem("freshAdminLogin");
    if (freshLogin === "true") {
      localStorage.removeItem("freshAdminLogin");
      const userData = localStorage.getItem("user");
      if (userData) {
        const parsedUser = JSON.parse(userData);
        toast.success(`Welcome to the admin dashboard, ${parsedUser.name || parsedUser.email}!`, {
          duration: 3000,
        });
      }
    }
  }, [checkAdminAuth]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
          <div className="mt-2 w-48 bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${loadingProgress}%` }}
            ></div>
          </div>
          <p className="mt-2 text-xs text-gray-500">
            {loadingProgress < 30
              ? "Checking authentication..."
              : loadingProgress < 70
              ? "Verifying admin access..."
              : "Loading dashboard data..."}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Admin Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">Complete business overview and management tools</p>
        </div>
        {user && (
          <div className="text-right">
            <p className="text-lg font-medium text-gray-900">{user.name || user.email}</p>
            <p className="text-sm text-gray-600">Administrator</p>
          </div>
        )}
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          <button
            onClick={() => setActiveTab("overview")}
            className={`${
              activeTab === "overview"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab("analytics")}
            className={`${
              activeTab === "analytics"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`}
          >
            Analytics
          </button>
        </nav>
      </div>

      {/* Dashboard Content */}
      <div className="space-y-6">
        {activeTab === "overview" && (
          <>
            <BookingAlerts />
            <DashboardOverview />
            <OrdersManagement limit={5} showViewAll={true} />
          </>
        )}
        {activeTab === "analytics" && <DashboardAnalytics />}
      </div>
    </div>
  );
}
