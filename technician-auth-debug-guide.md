# Technician Authentication Debug Guide

## समस्या:
```
Error: Failed to update availability: 403 Forbidden
Error: [ERROR] "Unauthorized"
```

## Changes Made:

### 1. Enhanced Token Debugging (SimpleAvailabilityToggle.tsx):

#### ✅ **Token Verification:**
```typescript
const getClientToken = (): string | null => {
  if (typeof window === "undefined") return null;
  const token = localStorage.getItem("token");
  console.log('SimpleAvailabilityToggle - Token check:', {
    hasToken: !!token,
    tokenLength: token?.length,
    tokenStart: token ? token.substring(0, 20) + '...' : 'none'
  });
  return token;
};
```

#### ✅ **User Role Verification:**
```typescript
// Check user role before making request
const userData = localStorage.getItem("user");
if (userData) {
  const parsedUser = JSON.parse(userData);
  console.log('SimpleAvailabilityToggle - User data:', {
    role: parsedUser.role,
    userId: parsedUser.id,
    email: parsedUser.email
  });
  
  if (parsedUser.role !== "technician") {
    toast.error("Access denied: Technician role required");
    return;
  }
}
```

### 2. Fallback API Implementation:

#### ✅ **Main API Fails → Fallback API:**
```typescript
// If main API fails, try fallback API
if (response.status === 403 || response.status === 401) {
  console.log('Trying fallback API due to auth error');
  try {
    const fallbackResponse = await fetch(`/api/technicians/toggle-availability/simple`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
      credentials: 'include',
    });
    
    if (fallbackResponse.ok) {
      const fallbackData = await fallbackResponse.json();
      if (fallbackData.success) {
        setIsAvailable(fallbackData.isAvailable);
        toast.success("You're now available for jobs (demo mode)");
        return;
      }
    }
  } catch (fallbackError) {
    console.error('Fallback API also failed:', fallbackError);
  }
}
```

### 3. Enhanced Error Messages:

#### ✅ **Specific Error Handling:**
```typescript
if (error instanceof Error) {
  if (error.message.includes("403")) {
    toast.error("Access denied. Please check your technician permissions.");
  } else if (error.message.includes("401")) {
    toast.error("Session expired. Please login again.");
  } else {
    toast.error(error.message);
  }
}
```

### 4. Debug Utility:

#### ✅ **Debug Button (Development Only):**
```typescript
const debugAuthState = () => {
  const token = localStorage.getItem("token");
  const userData = localStorage.getItem("user");
  console.log('Auth Debug:', {
    hasToken: !!token,
    hasUserData: !!userData,
    isAuthenticated,
    authLoading,
    userRole: user?.role,
    parsedUserData: userData ? JSON.parse(userData) : null
  });
};
```

## Debugging Steps:

### Step 1: Check Authentication State
1. **Technician dashboard** पर जाएं
2. **🐛 Debug button** click करें (development mode में)
3. **Browser console** check करें:
   ```
   Auth Debug: {
     hasToken: true/false,
     hasUserData: true/false,
     isAuthenticated: true/false,
     userRole: "technician",
     parsedUserData: {...}
   }
   ```

### Step 2: Check Token Validity
1. **Duty toggle** click करें
2. **Console logs** check करें:
   ```
   Token check: {
     hasToken: true,
     tokenLength: 150,
     tokenStart: "eyJhbGciOiJIUzI1NiIs..."
   }
   ```

### Step 3: Check User Role
1. **Console में user data** check करें:
   ```
   User data: {
     role: "technician",
     userId: "...",
     email: "..."
   }
   ```

### Step 4: API Response Analysis
1. **Network tab** में API calls check करें
2. **Response status** और **error messages** note करें

## Common Issues & Solutions:

### Issue 1: Token Missing
**Symptoms:**
```
Token check: { hasToken: false, tokenLength: undefined }
```
**Solution:**
```javascript
// Re-login
localStorage.clear();
// Go to login page and login again
```

### Issue 2: Wrong User Role
**Symptoms:**
```
User data: { role: "user", ... }  // Should be "technician"
```
**Solution:**
- Check if user is actually a technician in database
- Verify technician account creation

### Issue 3: Token Expired
**Symptoms:**
```
POST response status: 401
Error: Invalid or expired token
```
**Solution:**
```javascript
// Clear expired token and re-login
localStorage.removeItem("token");
localStorage.removeItem("user");
// Login again
```

### Issue 4: Database Connection
**Symptoms:**
```
POST response status: 500
Error: Database connection failed
```
**Solution:**
- Check MongoDB connection
- Verify technician record exists in database

## Manual Testing Commands:

### Test 1: Check Token in Console
```javascript
// In browser console
const token = localStorage.getItem("token");
console.log("Token:", token ? token.substring(0, 50) + "..." : "No token");

const user = localStorage.getItem("user");
console.log("User:", user ? JSON.parse(user) : "No user data");
```

### Test 2: Test API Directly
```javascript
// Test main API
fetch('/api/technicians/toggle-availability', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('token')}`,
    'Content-Type': 'application/json'
  },
  credentials: 'include'
}).then(r => r.json()).then(console.log).catch(console.error);
```

### Test 3: Test Fallback API
```javascript
// Test fallback API
fetch('/api/technicians/toggle-availability/simple', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('token')}`,
    'Content-Type': 'application/json'
  },
  credentials: 'include'
}).then(r => r.json()).then(console.log).catch(console.error);
```

## Expected Console Logs:

### ✅ **Successful Toggle:**
```
Token check: { hasToken: true, tokenLength: 150, tokenStart: "eyJ..." }
User data: { role: "technician", userId: "...", email: "..." }
Toggling availability, current state: true
POST response status: 200
POST response data: { success: true, isAvailable: false, message: "..." }
```

### ❌ **Failed Toggle (with Fallback):**
```
Token check: { hasToken: true, tokenLength: 150, tokenStart: "eyJ..." }
User data: { role: "technician", userId: "...", email: "..." }
Toggling availability, current state: true
POST response status: 403
POST request failed: 403 Forbidden
Trying fallback API due to auth error
Fallback API success: { success: true, isAvailable: false, fallback: true }
```

## Quick Fixes:

### Fix 1: Re-login
```javascript
localStorage.clear();
sessionStorage.clear();
// Go to /login and login again
```

### Fix 2: Check Database
- Verify technician record exists
- Check `isAvailable` field in technicians collection
- Ensure `status: "active"`

### Fix 3: Token Refresh
```javascript
// Force token refresh
localStorage.removeItem("token");
// Keep user data, just refresh token on next API call
```

अब duty toggle properly debug हो सकेगा और fallback mechanism भी काम करेगा! 🔧🐛
