"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>aP<PERSON>, Fa<PERSON>ser, FaMapMarkerAlt, FaCheckCircle, FaTimes, FaSpinner } from "react-icons/fa";

interface TechnicianFindingAnimationProps {
  isOpen: boolean;
  bookingDetails: {
    service: string;
    amount: number;
    customerName: string;
    address: string;
  };
  onTechnicianFound: (technician: any) => void;
  onNoTechnicianFound: () => void;
}

interface Technician {
  id: string;
  name: string;
  phone: string;
  rating: number;
  distance: string;
  avatar: string;
}

export default function TechnicianFindingAnimation({
  isOpen,
  bookingDetails,
  onTechnicianFound,
  onNoTechnicianFound
}: TechnicianFindingAnimationProps) {
  const [currentStage, setCurrentStage] = useState<'searching' | 'calling' | 'accepted' | 'rejected'>('searching');
  const [availableTechnicians, setAvailableTechnicians] = useState<Technician[]>([]);
  const [currentTechnician, setCurrentTechnician] = useState<Technician | null>(null);
  const [technicianIndex, setTechnicianIndex] = useState(0);
  const [timeLeft, setTimeLeft] = useState(40);
  const [callDuration, setCallDuration] = useState(0);
  const [isRinging, setIsRinging] = useState(false);

  // Fetch available technicians
  const fetchAvailableTechnicians = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/technicians/available', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.technicians) {
          const formattedTechnicians = data.technicians.map((tech: any) => ({
            id: tech._id,
            name: tech.name,
            phone: tech.phone,
            rating: tech.rating || 4.5,
            distance: `${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 9)} km`,
            avatar: "🧑‍🔧"
          }));
          setAvailableTechnicians(formattedTechnicians);
          return formattedTechnicians;
        }
      }
      return [];
    } catch (error) {
      console.error('Error fetching technicians:', error);
      return [];
    }
  };

  useEffect(() => {
    if (!isOpen) return;

    // Reset states
    setCurrentStage('searching');
    setTechnicianIndex(0);
    setTimeLeft(40);
    setCallDuration(0);
    setIsRinging(false);

    // Start the finding process
    const initProcess = async () => {
      const technicians = await fetchAvailableTechnicians();

      if (technicians.length === 0) {
        setTimeout(() => {
          setCurrentStage('rejected');
          onNoTechnicianFound();
        }, 3000);
        return;
      }

      setTimeout(() => {
        startCallingTechnician();
      }, 3000); // 3 seconds of searching animation
    };

    initProcess();
  }, [isOpen]);

  useEffect(() => {
    if (currentStage === 'calling' && timeLeft > 0) {
      const timer = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            // Time's up, try next technician or give up
            if (technicianIndex < availableTechnicians.length - 1) {
              setTechnicianIndex(prevIndex => prevIndex + 1);
              setCallDuration(0);
              setIsRinging(false);
              setTimeout(() => startCallingTechnician(), 1000);
            } else {
              // No more technicians
              setCurrentStage('rejected');
              setTimeout(() => onNoTechnicianFound(), 2000);
            }
            return 40;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [currentStage, timeLeft, technicianIndex, availableTechnicians.length]);

  useEffect(() => {
    if (currentStage === 'calling') {
      const callTimer = setInterval(() => {
        setCallDuration(prevDuration => {
          const newDuration = prevDuration + 1;

          // Simulate technician response after random time (8-15 seconds)
          const responseTime = 8 + Math.random() * 7;
          if (newDuration >= responseTime) {
            // 70% chance of acceptance
            const willAccept = Math.random() > 0.3;

            if (willAccept) {
              setCurrentStage('accepted');
              setIsRinging(false);
              setTimeout(() => {
                onTechnicianFound(currentTechnician);
              }, 3000);
            } else {
              setCurrentStage('rejected');
              setIsRinging(false);
              setTimeout(() => {
                if (technicianIndex < availableTechnicians.length - 1) {
                  setTechnicianIndex(prevIndex => prevIndex + 1);
                  setCallDuration(0);
                  setTimeout(() => startCallingTechnician(), 1000);
                } else {
                  onNoTechnicianFound();
                }
              }, 2000);
            }
          }

          return newDuration;
        });
      }, 1000);

      return () => clearInterval(callTimer);
    }
  }, [currentStage, technicianIndex, currentTechnician, availableTechnicians.length]);

  const startCallingTechnician = () => {
    if (technicianIndex < availableTechnicians.length) {
      const technician = availableTechnicians[technicianIndex];
      setCurrentTechnician(technician);
      setCurrentStage('calling');
      setCallDuration(0);
      setTimeLeft(40);
      setIsRinging(true);
    } else {
      // No more technicians available
      setCurrentStage('rejected');
      setTimeout(() => onNoTechnicianFound(), 2000);
    }
  };

  const getStageContent = () => {
    switch (currentStage) {
      case 'searching':
        return {
          title: "Finding Available Technicians",
          subtitle: "Searching for technicians near your location...",
          icon: <FaSpinner className="h-16 w-16 text-blue-600 animate-spin" />,
          bgColor: "bg-blue-50"
        };
      case 'calling':
        return {
          title: `Calling ${currentTechnician?.name}`,
          subtitle: `Ringing ${currentTechnician?.phone}...`,
          icon: <FaPhone className={`h-16 w-16 text-green-600 ${isRinging ? 'animate-bounce' : ''}`} />,
          bgColor: "bg-green-50"
        };
      case 'accepted':
        return {
          title: "Technician Found!",
          subtitle: `${currentTechnician?.name} has accepted your booking`,
          icon: <FaCheckCircle className="h-16 w-16 text-green-600" />,
          bgColor: "bg-green-50"
        };
      case 'rejected':
        return {
          title: "Technician Busy",
          subtitle: "Looking for another technician...",
          icon: <FaTimes className="h-16 w-16 text-red-600" />,
          bgColor: "bg-red-50"
        };
      default:
        return {
          title: "Processing",
          subtitle: "Please wait...",
          icon: <FaSpinner className="h-16 w-16 text-blue-600 animate-spin" />,
          bgColor: "bg-blue-50"
        };
    }
  };

  const content = getStageContent();

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className={`bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 text-center ${content.bgColor} border-2`}
          >
            {/* Service Info */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">{bookingDetails.service}</h3>
              <p className="text-gray-600">₹{bookingDetails.amount} • {bookingDetails.customerName}</p>
              <div className="flex items-center justify-center mt-2 text-sm text-gray-500">
                <FaMapMarkerAlt className="mr-1" />
                <span className="truncate">{bookingDetails.address}</span>
              </div>
            </div>

            {/* Main Animation */}
            <div className="relative mb-6">
              {/* Ripple Effect for Calling */}
              {currentStage === 'calling' && (
                <>
                  <motion.div
                    className="absolute inset-0 bg-green-400 rounded-full opacity-20"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.2, 0.4, 0.2]
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  <motion.div
                    className="absolute inset-0 bg-green-400 rounded-full opacity-10"
                    animate={{
                      scale: [1, 2, 1],
                      opacity: [0.1, 0.3, 0.1]
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 0.3
                    }}
                  />
                </>
              )}

              {/* Icon */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{
                  delay: 0.2,
                  type: "spring",
                  stiffness: 200
                }}
                className="relative z-10 mb-4"
              >
                {content.icon}
              </motion.div>
            </div>

            {/* Title */}
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-2xl font-bold text-gray-800 mb-2"
            >
              {content.title}
            </motion.h2>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-gray-600 mb-6"
            >
              {content.subtitle}
            </motion.p>

            {/* Technician Card (when calling) */}
            {currentStage === 'calling' && currentTechnician && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white p-4 rounded-lg shadow-md mb-4"
              >
                <div className="flex items-center space-x-3">
                  <div className="text-3xl">{currentTechnician.avatar}</div>
                  <div className="flex-1 text-left">
                    <h4 className="font-semibold text-gray-800">{currentTechnician.name}</h4>
                    <p className="text-sm text-gray-600">⭐ {currentTechnician.rating} • {currentTechnician.distance} away</p>
                    <p className="text-xs text-gray-500">{currentTechnician.phone}</p>
                  </div>
                  <motion.div
                    animate={{ rotate: isRinging ? [0, 10, -10, 0] : 0 }}
                    transition={{ duration: 0.5, repeat: isRinging ? Infinity : 0 }}
                  >
                    <FaPhone className="text-green-600" />
                  </motion.div>
                </div>
              </motion.div>
            )}

            {/* Progress/Timer */}
            {currentStage === 'calling' && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Calling... {callDuration}s</span>
                  <span>{timeLeft}s remaining</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <motion.div
                    className="bg-green-600 h-2 rounded-full"
                    initial={{ width: "0%" }}
                    animate={{ width: `${((40 - timeLeft) / 40) * 100}%` }}
                    transition={{ duration: 0.5 }}
                  />
                </div>
              </div>
            )}

            {/* Searching Animation */}
            {currentStage === 'searching' && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="space-y-4"
              >
                <div className="flex justify-center space-x-2">
                  {[0, 1, 2].map((i) => (
                    <motion.div
                      key={i}
                      className="w-3 h-3 bg-blue-600 rounded-full"
                      animate={{
                        scale: [1, 1.5, 1],
                        opacity: [0.5, 1, 0.5]
                      }}
                      transition={{
                        duration: 1,
                        repeat: Infinity,
                        delay: i * 0.2
                      }}
                    />
                  ))}
                </div>
                <p className="text-sm text-gray-500">
                  Looking for technicians in your area...
                </p>
              </motion.div>
            )}

            {/* Success Message */}
            {currentStage === 'accepted' && currentTechnician && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-green-100 p-4 rounded-lg"
              >
                <p className="text-green-800 font-medium">
                  {currentTechnician.name} will contact you shortly to confirm the appointment time.
                </p>
              </motion.div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
