"use client";

import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaCheckCircle, FaTimes, FaPhone, FaUser, FaMapMarkerAlt, FaSync } from "react-icons/fa";
import { toast } from "react-hot-toast";

interface BookingStatusProps {
  isOpen: boolean;
  bookingId: string;
  bookingDetails: {
    service: string;
    amount: number;
    customerName: string;
    address: string;
  };
  onBookingConfirmed: (technician: any) => void;
  onBookingFailed: () => void;
  onTryAgain: () => void;
}

interface BookingStatus {
  status: 'processing' | 'confirmed' | 'rejected' | 'no_technician' | 'expired';
  technician?: {
    name: string;
    phone: string;
    rating: number;
    distance: string;
  };
  message?: string;
  updatedAt?: string;
}

export default function RealTimeBookingStatus({
  isOpen,
  bookingId,
  bookingDetails,
  onBookingConfirmed,
  onBookingFailed,
  onTryAgain
}: BookingStatusProps) {
  const [bookingStatus, setBookingStatus] = useState<BookingStatus>({ status: 'processing' });
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [isPolling, setIsPolling] = useState(false);

  // Poll booking status
  const pollBookingStatus = useCallback(async () => {
    if (!bookingId || !isOpen) return;

    try {
      console.log('Polling booking status for:', bookingId);

      const response = await fetch(`/api/bookings/${bookingId}/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getClientToken()}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Booking status response:', data);
        
        if (data.success) {
          const newStatus: BookingStatus = {
            status: data.booking.status === 'confirmed' ? 'confirmed' : 
                   data.booking.status === 'rejected' ? 'rejected' :
                   data.booking.status === 'no_technician_available' ? 'no_technician' :
                   'processing',
            technician: data.booking.technicianName ? {
              name: data.booking.technicianName,
              phone: data.booking.technicianPhone || '',
              rating: 4.5, // Default rating
              distance: '2.3 km' // Default distance
            } : undefined,
            message: data.booking.statusMessage || '',
            updatedAt: data.booking.updatedAt
          };

          setBookingStatus(newStatus);

          // Handle status changes
          if (newStatus.status === 'confirmed' && newStatus.technician) {
            setIsPolling(false);
            setTimeout(() => {
              onBookingConfirmed(newStatus.technician);
            }, 2000);
          } else if (newStatus.status === 'rejected' || newStatus.status === 'no_technician') {
            setIsPolling(false);
            setTimeout(() => {
              onBookingFailed();
            }, 3000);
          }
        }
      } else {
        console.error('Failed to fetch booking status:', response.status);
      }
    } catch (error) {
      console.error('Error polling booking status:', error);
    }
  }, [bookingId, isOpen, onBookingConfirmed, onBookingFailed]);

  // Start polling when component opens
  useEffect(() => {
    if (isOpen && bookingId) {
      setIsPolling(true);
      setTimeElapsed(0);
      setBookingStatus({ status: 'processing' });
      
      // Initial poll
      pollBookingStatus();
      
      // Set up polling interval
      const pollInterval = setInterval(pollBookingStatus, 3000); // Poll every 3 seconds
      
      return () => {
        clearInterval(pollInterval);
        setIsPolling(false);
      };
    }
  }, [isOpen, bookingId, pollBookingStatus]);

  // Timer for elapsed time
  useEffect(() => {
    if (isOpen && bookingStatus.status === 'processing') {
      const timer = setInterval(() => {
        setTimeElapsed(prev => {
          const newTime = prev + 1;
          
          // Auto-expire after 60 seconds
          if (newTime >= 60) {
            setBookingStatus({ status: 'expired' });
            setIsPolling(false);
            setTimeout(() => onBookingFailed(), 2000);
          }
          
          return newTime;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [isOpen, bookingStatus.status, onBookingFailed]);

  const getStatusContent = () => {
    switch (bookingStatus.status) {
      case 'processing':
        return {
          icon: <FaSpinner className="h-16 w-16 text-blue-600 animate-spin" />,
          title: "Finding Technician",
          subtitle: "Looking for available technicians near you...",
          bgColor: "bg-blue-50",
          borderColor: "border-blue-200",
          showTimer: true
        };
      case 'confirmed':
        return {
          icon: <FaCheckCircle className="h-16 w-16 text-green-600" />,
          title: "Booking Confirmed!",
          subtitle: `${bookingStatus.technician?.name} has accepted your booking`,
          bgColor: "bg-green-50",
          borderColor: "border-green-200",
          showTimer: false
        };
      case 'rejected':
        return {
          icon: <FaTimes className="h-16 w-16 text-red-600" />,
          title: "Technician Unavailable",
          subtitle: "The technician couldn't accept your booking",
          bgColor: "bg-red-50",
          borderColor: "border-red-200",
          showTimer: false
        };
      case 'no_technician':
        return {
          icon: <FaTimes className="h-16 w-16 text-orange-600" />,
          title: "No Technician Available",
          subtitle: "No technicians are available in your area right now",
          bgColor: "bg-orange-50",
          borderColor: "border-orange-200",
          showTimer: false
        };
      case 'expired':
        return {
          icon: <FaTimes className="h-16 w-16 text-gray-600" />,
          title: "Booking Expired",
          subtitle: "No response from technicians. Please try again.",
          bgColor: "bg-gray-50",
          borderColor: "border-gray-200",
          showTimer: false
        };
      default:
        return {
          icon: <FaSpinner className="h-16 w-16 text-blue-600 animate-spin" />,
          title: "Processing",
          subtitle: "Please wait...",
          bgColor: "bg-blue-50",
          borderColor: "border-blue-200",
          showTimer: false
        };
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleTryAgain = () => {
    setBookingStatus({ status: 'processing' });
    setTimeElapsed(0);
    setIsPolling(true);
    onTryAgain();
  };

  const content = getStatusContent();

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className={`bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 text-center ${content.bgColor} border-2 ${content.borderColor}`}
          >
            {/* Service Info */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">{bookingDetails.service}</h3>
              <p className="text-gray-600">₹{bookingDetails.amount} • {bookingDetails.customerName}</p>
              <div className="flex items-center justify-center mt-2 text-sm text-gray-500">
                <FaMapMarkerAlt className="mr-1" />
                <span className="truncate">{bookingDetails.address}</span>
              </div>
            </div>

            {/* Status Icon with Animation */}
            <div className="relative mb-6">
              {bookingStatus.status === 'processing' && (
                <motion.div
                  className="absolute inset-0 bg-blue-400 rounded-full opacity-20"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.2, 0.4, 0.2]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              )}
              
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ 
                  delay: 0.2, 
                  type: "spring", 
                  stiffness: 200 
                }}
                className="relative z-10 mb-4"
              >
                {content.icon}
              </motion.div>
            </div>

            {/* Title */}
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-2xl font-bold text-gray-800 mb-2"
            >
              {content.title}
            </motion.h2>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-gray-600 mb-6"
            >
              {content.subtitle}
            </motion.p>

            {/* Timer (for processing status) */}
            {content.showTimer && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mb-6"
              >
                <div className="text-2xl font-mono text-blue-600 mb-2">
                  {formatTime(timeElapsed)}
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <motion.div
                    className="bg-blue-600 h-2 rounded-full"
                    initial={{ width: "0%" }}
                    animate={{ width: `${(timeElapsed / 60) * 100}%` }}
                    transition={{ duration: 0.5 }}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  Maximum wait time: 1 minute
                </p>
              </motion.div>
            )}

            {/* Technician Info (when confirmed) */}
            {bookingStatus.status === 'confirmed' && bookingStatus.technician && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white p-4 rounded-lg shadow-md mb-4"
              >
                <div className="flex items-center space-x-3">
                  <div className="text-3xl">🧑‍🔧</div>
                  <div className="flex-1 text-left">
                    <h4 className="font-semibold text-gray-800">{bookingStatus.technician.name}</h4>
                    <p className="text-sm text-gray-600">⭐ {bookingStatus.technician.rating} • {bookingStatus.technician.distance} away</p>
                    <p className="text-xs text-gray-500">{bookingStatus.technician.phone}</p>
                  </div>
                  <FaPhone className="text-green-600" />
                </div>
              </motion.div>
            )}

            {/* Action Buttons */}
            {(bookingStatus.status === 'rejected' || bookingStatus.status === 'no_technician' || bookingStatus.status === 'expired') && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-3"
              >
                <button
                  onClick={handleTryAgain}
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium flex items-center justify-center"
                >
                  <FaSync className="mr-2" />
                  Try Again
                </button>
                <button
                  onClick={onBookingFailed}
                  className="w-full bg-gray-200 text-gray-800 py-3 px-6 rounded-lg hover:bg-gray-300 transition-colors font-medium"
                >
                  Cancel Booking
                </button>
              </motion.div>
            )}

            {/* Processing Message */}
            {bookingStatus.status === 'processing' && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-sm text-gray-500"
              >
                <p>We're notifying technicians in your area...</p>
                <p className="mt-1">Please don't close this window</p>
              </motion.div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
function getClientToken(): string {
  throw new Error("Function not implemented.");
}

