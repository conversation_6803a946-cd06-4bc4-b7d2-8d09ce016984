{"version": 2, "buildCommand": "npm run build", "installCommand": "npm install", "framework": "nextjs", "regions": ["bom1"], "functions": {"app/api/**/*": {"memory": 1024, "maxDuration": 60}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}]}