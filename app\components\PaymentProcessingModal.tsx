"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FaCreditCard, FaS<PERSON>ner, FaCheckCircle, FaTimes, FaShieldAlt, FaLock } from "react-icons/fa";

interface PaymentProcessingModalProps {
  isOpen: boolean;
  stage: 'processing' | 'success' | 'error';
  message?: string;
  onClose?: () => void;
  processingDuration?: number; // in seconds, default 40
}

export default function PaymentProcessingModal({
  isOpen,
  stage,
  message,
  onClose,
  processingDuration = 40
}: PaymentProcessingModalProps) {
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);

  // Processing steps for animation
  const processingSteps = [
    { text: "Initializing secure payment gateway...", duration: 3 },
    { text: "Verifying payment details...", duration: 5 },
    { text: "Connecting to bank servers...", duration: 8 },
    { text: "Processing transaction...", duration: 12 },
    { text: "Validating payment...", duration: 8 },
    { text: "Finalizing transaction...", duration: 4 }
  ];

  // Timer for processing animation
  useEffect(() => {
    if (isOpen && stage === 'processing') {
      setTimeElapsed(0);
      setCurrentStep(0);

      const timer = setInterval(() => {
        setTimeElapsed(prev => {
          const newTime = prev + 1;

          // Update current step based on elapsed time
          let totalDuration = 0;
          for (let i = 0; i < processingSteps.length; i++) {
            totalDuration += processingSteps[i].duration;
            if (newTime <= totalDuration) {
              setCurrentStep(i);
              break;
            }
          }

          return newTime;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [isOpen, stage, processingSteps]);

  const getStageContent = () => {
    switch (stage) {
      case 'processing':
        return {
          icon: <FaSpinner className="h-16 w-16 text-blue-600 animate-spin" />,
          title: "Processing Payment",
          subtitle: processingSteps[currentStep]?.text || "Please wait while we process your payment...",
          bgColor: "bg-blue-50",
          borderColor: "border-blue-200"
        };
      case 'success':
        return {
          icon: (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: [0, 1.2, 1] }}
              transition={{ duration: 0.6, ease: "easeOut" }}
            >
              <FaCheckCircle className="h-16 w-16 text-green-600" />
            </motion.div>
          ),
          title: "Payment Successful!",
          subtitle: message || "Your payment has been processed successfully",
          bgColor: "bg-green-50",
          borderColor: "border-green-200"
        };
      case 'error':
        return {
          icon: <FaTimes className="h-16 w-16 text-red-600" />,
          title: "Payment Failed",
          subtitle: message || "There was an error processing your payment",
          bgColor: "bg-red-50",
          borderColor: "border-red-200"
        };
      default:
        return {
          icon: <FaSpinner className="h-16 w-16 text-blue-600 animate-spin" />,
          title: "Processing",
          subtitle: "Please wait...",
          bgColor: "bg-blue-50",
          borderColor: "border-blue-200"
        };
    }
  };

  const content = getStageContent();

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className={`bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 text-center ${content.bgColor} border-2 ${content.borderColor}`}
          >
            {/* Animated Background */}
            <div className="relative">
              {stage === 'processing' && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full opacity-20"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.2, 0.4, 0.2]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              )}

              {/* Icon */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{
                  delay: 0.2,
                  type: "spring",
                  stiffness: 200
                }}
                className="relative z-10 mb-6"
              >
                {content.icon}
              </motion.div>
            </div>

            {/* Title */}
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-2xl font-bold text-gray-800 mb-2"
            >
              {content.title}
            </motion.h2>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-gray-600 mb-6"
            >
              {content.subtitle}
            </motion.p>

            {/* Processing Animation */}
            {stage === 'processing' && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="space-y-6"
              >
                {/* Progress Bar */}
                <div className="space-y-3">
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>Progress</span>
                    <span>{Math.round((timeElapsed / processingDuration) * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <motion.div
                      className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full"
                      initial={{ width: "0%" }}
                      animate={{ width: `${(timeElapsed / processingDuration) * 100}%` }}
                      transition={{ duration: 0.5, ease: "easeOut" }}
                    />
                  </div>
                  <div className="text-center">
                    <span className="text-lg font-mono text-blue-600">
                      {Math.max(0, processingDuration - timeElapsed)}s
                    </span>
                    <span className="text-sm text-gray-500 ml-2">remaining</span>
                  </div>
                </div>

                {/* Processing Steps */}
                <div className="space-y-3">
                  {processingSteps.map((step, index) => {
                    const isActive = index === currentStep;
                    const isCompleted = index < currentStep;

                    return (
                      <motion.div
                        key={index}
                        className={`flex items-center space-x-3 p-2 rounded-lg transition-colors ${
                          isActive ? 'bg-blue-100 border border-blue-200' :
                          isCompleted ? 'bg-green-50' : 'bg-gray-50'
                        }`}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.6 + (index * 0.1) }}
                      >
                        <div className={`w-3 h-3 rounded-full flex-shrink-0 ${
                          isCompleted ? 'bg-green-500' :
                          isActive ? 'bg-blue-500 animate-pulse' : 'bg-gray-300'
                        }`} />
                        <span className={`text-sm ${
                          isActive ? 'text-blue-700 font-medium' :
                          isCompleted ? 'text-green-700' : 'text-gray-500'
                        }`}>
                          {step.text}
                        </span>
                        {isActive && (
                          <FaSpinner className="w-3 h-3 text-blue-500 animate-spin ml-auto" />
                        )}
                        {isCompleted && (
                          <FaCheckCircle className="w-3 h-3 text-green-500 ml-auto" />
                        )}
                      </motion.div>
                    );
                  })}
                </div>

                {/* Animated Dots */}
                <motion.div
                  className="flex items-center justify-center space-x-2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                >
                  <motion.div
                    className="w-2 h-2 bg-blue-600 rounded-full"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.5, 1, 0.5]
                    }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      delay: 0
                    }}
                  />
                  <motion.div
                    className="w-2 h-2 bg-blue-600 rounded-full"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.5, 1, 0.5]
                    }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      delay: 0.2
                    }}
                  />
                  <motion.div
                    className="w-2 h-2 bg-blue-600 rounded-full"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.5, 1, 0.5]
                    }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      delay: 0.4
                    }}
                  />
                </motion.div>

                {/* Security Badges */}
                <div className="space-y-2">
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 }}
                    className="flex items-center justify-center space-x-2 text-xs text-gray-500"
                  >
                    <FaShieldAlt className="h-3 w-3 text-green-500" />
                    <span>Secured by 256-bit SSL encryption</span>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.9 }}
                    className="flex items-center justify-center space-x-2 text-xs text-gray-500"
                  >
                    <FaLock className="h-3 w-3 text-blue-500" />
                    <span>PCI DSS Compliant Payment Gateway</span>
                  </motion.div>
                </div>

                {/* Warning */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.0 }}
                  className="bg-yellow-50 border border-yellow-200 rounded-lg p-3"
                >
                  <p className="text-sm text-yellow-800 text-center">
                    ⚠️ Do not close this window or press back button
                  </p>
                  <p className="text-xs text-yellow-600 text-center mt-1">
                    Transaction in progress - Please wait
                  </p>
                </motion.div>
              </motion.div>
            )}

            {/* Success/Error Actions */}
            {(stage === 'success' || stage === 'error') && onClose && (
              <motion.button
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                onClick={onClose}
                className={`w-full py-3 px-6 rounded-lg font-medium transition-colors ${
                  stage === 'success'
                    ? 'bg-green-600 text-white hover:bg-green-700'
                    : 'bg-red-600 text-white hover:bg-red-700'
                }`}
              >
                {stage === 'success' ? 'Continue' : 'Try Again'}
              </motion.button>
            )}

            {/* Auto-close timer for success */}
            {stage === 'success' && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
                className="mt-4"
              >
                <div className="text-xs text-gray-500">
                  Redirecting to booking confirmation...
                </div>
                <motion.div
                  className="w-full bg-gray-200 rounded-full h-1 mt-2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  <motion.div
                    className="bg-green-600 h-1 rounded-full"
                    initial={{ width: "0%" }}
                    animate={{ width: "100%" }}
                    transition={{ duration: 3, ease: "linear" }}
                  />
                </motion.div>
              </motion.div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
