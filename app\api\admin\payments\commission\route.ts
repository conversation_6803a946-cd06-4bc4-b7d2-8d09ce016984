import { NextResponse } from "next/server";
import { connectToDatabase } from "@/app/lib/mongodb";
import { verifyToken, getTokenFromRequest } from "@/app/lib/auth";
import { ObjectId } from "mongodb";

interface CategoryCommission {
  categoryId: string;
  categoryName: string;
  commissionRate: number;
  isActive: boolean;
  updatedAt: string;
  updatedBy: string;
}

interface CommissionSettings {
  _id?: ObjectId;
  type: "category-based" | "global";
  globalRate?: number;
  categoryRates?: CategoryCommission[];
  defaultRate: number;
  createdAt: string;
  updatedAt: string;
  updatedBy: string;
}

// Get commission settings (category-based or global)
export async function GET(request: Request) {
  try {
    const token = getTokenFromRequest(request);
    
    if (!token) {
      return NextResponse.json(
        { success: false, message: "Authentication required" },
        { status: 401 }
      );
    }

    const decoded = verifyToken(token);
    if (!decoded || (decoded as {role?: string}).role !== "admin") {
      return NextResponse.json(
        { success: false, message: "Unauthorized access" },
        { status: 403 }
      );
    }

    const { db } = await connectToDatabase();

    // Get commission settings
    const commissionSettings = await db.collection("commissionSettings").findOne({});
    
    // Get service categories
    const categories = await db.collection("serviceCategories")
      .find({ isActive: true })
      .sort({ order: 1 })
      .toArray();

    if (!commissionSettings) {
      // Create default settings if none exist
      const defaultSettings: CommissionSettings = {
        type: "global",
        globalRate: 30,
        defaultRate: 30,
        categoryRates: categories.map(cat => ({
          categoryId: cat._id.toString(),
          categoryName: cat.name,
          commissionRate: 30,
          isActive: true,
          updatedAt: new Date().toISOString(),
          updatedBy: (decoded as {userId?: string}).userId || "admin"
        })),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        updatedBy: (decoded as {userId?: string}).userId || "admin"
      };

      await db.collection("commissionSettings").insertOne(defaultSettings);
      
      return NextResponse.json({
        success: true,
        settings: defaultSettings,
        categories
      });
    }

    return NextResponse.json({
      success: true,
      settings: commissionSettings,
      categories
    });
  } catch (error) {
    console.error("Error fetching commission settings:", error);
    return NextResponse.json(
      { success: false, message: "Failed to fetch commission settings" },
      { status: 500 }
    );
  }
}

// Update commission settings
export async function POST(request: Request) {
  try {
    const token = getTokenFromRequest(request);
    
    if (!token) {
      return NextResponse.json(
        { success: false, message: "Authentication required" },
        { status: 401 }
      );
    }

    const decoded = verifyToken(token);
    if (!decoded || (decoded as {role?: string}).role !== "admin") {
      return NextResponse.json(
        { success: false, message: "Unauthorized access" },
        { status: 403 }
      );
    }

    const { type, globalRate, categoryRates, defaultRate } = await request.json();

    // Validate input
    if (!type || !["category-based", "global"].includes(type)) {
      return NextResponse.json(
        { success: false, message: "Invalid commission type" },
        { status: 400 }
      );
    }

    if (type === "global" && (typeof globalRate !== "number" || globalRate < 0 || globalRate > 100)) {
      return NextResponse.json(
        { success: false, message: "Invalid global commission rate" },
        { status: 400 }
      );
    }

    if (type === "category-based" && (!Array.isArray(categoryRates) || categoryRates.length === 0)) {
      return NextResponse.json(
        { success: false, message: "Category rates are required for category-based commission" },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();

    // Get current settings for logging
    const currentSettings = await db.collection("commissionSettings").findOne({});

    const updatedSettings: CommissionSettings = {
      type,
      globalRate: type === "global" ? globalRate : undefined,
      categoryRates: type === "category-based" ? categoryRates.map((rate: any) => ({
        ...rate,
        updatedAt: new Date().toISOString(),
        updatedBy: (decoded as {userId?: string}).userId || "admin"
      })) : undefined,
      defaultRate: defaultRate || 30,
      createdAt: currentSettings?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      updatedBy: (decoded as {userId?: string}).userId || "admin"
    };

    // Update commission settings
    await db.collection("commissionSettings").replaceOne(
      {},
      updatedSettings,
      { upsert: true }
    );

    // Log admin activity
    await db.collection("adminActivity").insertOne({
      action: "update_commission_settings",
      adminId: (decoded as {userId?: string}).userId,
      details: {
        oldSettings: currentSettings,
        newSettings: updatedSettings,
        type: type
      },
      createdAt: new Date().toISOString()
    });

    return NextResponse.json({
      success: true,
      message: "Commission settings updated successfully",
      settings: updatedSettings
    });
  } catch (error) {
    console.error("Error updating commission settings:", error);
    return NextResponse.json(
      { success: false, message: "Failed to update commission settings" },
      { status: 500 }
    );
  }
}

// Get commission history
export async function PUT(request: Request) {
  try {
    const token = getTokenFromRequest(request);
    
    if (!token) {
      return NextResponse.json(
        { success: false, message: "Authentication required" },
        { status: 401 }
      );
    }

    const decoded = verifyToken(token);
    if (!decoded || (decoded as {role?: string}).role !== "admin") {
      return NextResponse.json(
        { success: false, message: "Unauthorized access" },
        { status: 403 }
      );
    }

    const { db } = await connectToDatabase();

    // Get commission history from admin activity log
    const history = await db.collection("adminActivity")
      .find({ action: "update_commission_settings" })
      .sort({ createdAt: -1 })
      .limit(20)
      .toArray();

    return NextResponse.json({
      success: true,
      history: history.map(item => ({
        type: item.details.newSettings.type,
        changes: item.details,
        updatedAt: item.createdAt,
        adminId: item.adminId
      }))
    });
  } catch (error) {
    console.error("Error fetching commission history:", error);
    return NextResponse.json(
      { success: false, message: "Failed to fetch commission history" },
      { status: 500 }
    );
  }
}
