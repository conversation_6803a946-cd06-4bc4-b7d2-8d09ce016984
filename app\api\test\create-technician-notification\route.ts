import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/app/lib/mongodb";

export async function POST(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();

    // Get all active technicians
    const technicians = await db
      .collection("technicians")
      .find({
        status: "active"
      })
      .toArray();

    console.log(`Found ${technicians.length} technicians`);

    if (technicians.length === 0) {
      return NextResponse.json({
        success: false,
        message: "No technicians found"
      });
    }

    // Create test regular notifications for each technician
    const regularNotifications = technicians.map(technician => ({
      recipientId: technician._id.toString(),
      recipientType: "technician",
      title: "Test System Notification",
      message: "This is a test notification to verify the notification system is working properly.",
      type: "system",
      read: false,
      createdAt: new Date(),
      updatedAt: new Date()
    }));

    // Create test job notifications for each technician
    const jobNotifications = technicians.map(technician => ({
      bookingId: "TEST_BOOKING_" + Date.now(),
      bookingIdDisplay: "TEST_" + Math.random().toString(36).substr(2, 6).toUpperCase(),
      technicianId: technician._id.toString(),
      serviceName: "Test AC Repair",
      customerName: "Test Customer",
      customerPhone: "+91 9876543210",
      address: "Test Address, Test City, Test State",
      amount: 799,
      urgency: "normal",
      status: "pending",
      scheduledDate: new Date().toISOString().split('T')[0],
      scheduledTime: "2:00 PM - 4:00 PM",
      description: "Test AC repair service - cooling issue",
      paymentMethod: "online",
      distance: 3,
      estimatedDuration: "2-3 hours",
      notifiedTechnicians: [technician._id.toString()],
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
    }));

    // Insert regular notifications
    const regularResult = await db.collection("notifications").insertMany(regularNotifications);
    
    // Insert job notifications
    const jobResult = await db.collection("job_notifications").insertMany(jobNotifications);

    console.log(`✅ Created ${regularResult.insertedCount} regular notifications and ${jobResult.insertedCount} job notifications`);

    return NextResponse.json({
      success: true,
      message: `Created ${regularResult.insertedCount} regular notifications and ${jobResult.insertedCount} job notifications`,
      technicians: technicians.map(t => ({
        id: t._id.toString(),
        name: t.name,
        phone: t.phone,
        status: t.status
      })),
      notifications: {
        regular: regularNotifications.length,
        jobs: jobNotifications.length,
        total: regularNotifications.length + jobNotifications.length
      }
    });

  } catch (error) {
    console.error('Error creating test technician notifications:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: "Failed to create test technician notifications",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
