import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/app/lib/mongodb";

export async function POST(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();

    // Get all active technicians
    const technicians = await db
      .collection("technicians")
      .find({
        status: "active",
        isAvailable: true
      })
      .toArray();

    console.log(`Found ${technicians.length} active technicians`);

    if (technicians.length === 0) {
      return NextResponse.json({
        success: false,
        message: "No active technicians found"
      });
    }

    // Create test job notifications
    const testNotifications = technicians.map(technician => ({
      bookingId: "TEST_BOOKING_" + Date.now(),
      bookingIdDisplay: "TEST_" + Math.random().toString(36).substr(2, 6).toUpperCase(),
      technicianId: technician._id.toString(),
      serviceName: "Test Refrigerator Repair",
      customerName: "Test Customer",
      customerPhone: "+91 9876543210",
      address: "Test Address, Test City",
      amount: 599,
      urgency: "normal",
      status: "pending",
      scheduledDate: new Date().toISOString().split('T')[0],
      scheduledTime: "10:00 AM - 12:00 PM",
      description: "Test refrigerator repair service",
      paymentMethod: "online",
      distance: 2,
      estimatedDuration: "1-2 hours",
      notifiedTechnicians: [technician._id.toString()],
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
    }));

    // Insert test notifications
    const result = await db.collection("job_notifications").insertMany(testNotifications);

    console.log(`✅ Created ${result.insertedCount} test job notifications`);

    return NextResponse.json({
      success: true,
      message: `Created ${result.insertedCount} test job notifications`,
      technicians: technicians.map(t => ({
        id: t._id.toString(),
        name: t.name,
        phone: t.phone,
        isAvailable: t.isAvailable
      })),
      notifications: testNotifications.map(n => ({
        technicianId: n.technicianId,
        serviceName: n.serviceName,
        bookingIdDisplay: n.bookingIdDisplay,
        amount: n.amount
      }))
    });

  } catch (error) {
    console.error('Error creating test job notifications:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: "Failed to create test job notifications",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
