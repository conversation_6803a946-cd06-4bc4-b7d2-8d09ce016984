{"css.validate": false, "less.validate": false, "scss.validate": false, "css.customData": [".vscode/css_custom_data.json"], "tailwindCSS.includeLanguages": {"plaintext": "html", "javascript": "javascript", "typescript": "typescript"}, "tailwindCSS.experimental.classRegex": ["class[:]\\s*['\"`]([^'\"`]*)['\"`]", "className[:]\\s*['\"`]([^'\"`]*)['\"`]", "tw`([^`]*)", "tw=\"([^\"]*)", "tw={'([^'}]*)", "tw\\.\\w+`([^`]*)", "tw\\(.*?\\)`([^`]*)"], "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": true}, "editor.autoClosingBrackets": "always", "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "editor.inlineSuggest.enabled": true, "tailwindCSS.emmetCompletions": true}