# Email Configuration Guide

## समस्या:
आ<PERSON><PERSON><PERSON> logs में दिख रहा है कि email configuration incomplete है:
```
[WARN] Email configuration incomplete { hasEmailUser: true, hasEmailPass: true, hasAdminEmail: false }
```

## समाधान:

### 1. Environment Variables Setup:

आपको ये environment variables set करने होंगे:

#### ✅ **Required for Customer Emails:**
```bash
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_SERVICE=gmail
```

#### ✅ **Required for Admin Notifications:**
```bash
ADMIN_EMAIL=<EMAIL>
```

### 2. Gmail App Password Setup:

#### Step 1: Enable 2-Factor Authentication
1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Security → 2-Step Verification → Turn On

#### Step 2: Generate App Password
1. Go to Security → App passwords
2. Select app: "Mail"
3. Select device: "Other (custom name)" → "Dizit Solutions"
4. Copy the 16-character password
5. Use this as `EMAIL_PASS`

### 3. Environment Variables File:

Create/Update `.env.local`:
```bash
# Email Configuration
EMAIL_USER=<EMAIL>
EMAIL_PASS=abcd-efgh-ijkl-mnop  # 16-character app password
EMAIL_SERVICE=gmail
ADMIN_EMAIL=<EMAIL>

# Optional: Control admin emails
SEND_ADMIN_BOOKING_EMAILS=true
```

### 4. Vercel Deployment:

If deploying on Vercel, add these in:
**Vercel Dashboard → Project → Settings → Environment Variables**

```
EMAIL_USER = <EMAIL>
EMAIL_PASS = abcd-efgh-ijkl-mnop
EMAIL_SERVICE = gmail
ADMIN_EMAIL = <EMAIL>
SEND_ADMIN_BOOKING_EMAILS = true
```

## Changes Made in Code:

### 1. Improved Email Configuration Check:
```typescript
// Before: Required all three (EMAIL_USER, EMAIL_PASS, ADMIN_EMAIL)
// After: Separate checks for customer vs admin emails

const basicEmailConfigured = !!(EMAIL_USER && EMAIL_PASS);
const fullEmailConfigured = !!(EMAIL_USER && EMAIL_PASS && ADMIN_EMAIL);
```

### 2. Customer Email Functions:
- `sendBookingConfirmationEmail()` - Only needs basic config
- `sendPasswordResetEmail()` - Only needs basic config
- `sendTechnicianCredentialsEmail()` - Only needs basic config

### 3. Admin Email Functions:
- `sendAdminBookingNotificationEmail()` - Needs full config (including ADMIN_EMAIL)

### 4. Graceful Fallbacks:
```typescript
// Customer emails: Skip if basic config missing
if (!basicEmailConfigured) {
  logger.warn("Email sending skipped - basic email configuration missing");
  return false;
}

// Admin emails: Skip if ADMIN_EMAIL missing
if (!fullEmailConfigured) {
  logger.warn("Admin email sending skipped - ADMIN_EMAIL not configured");
  return false;
}
```

## Testing:

### Test 1: Customer Email (Basic Config)
1. Set `EMAIL_USER` and `EMAIL_PASS`
2. Make a booking
3. **Expected**: Customer gets confirmation email

### Test 2: Admin Email (Full Config)
1. Set `EMAIL_USER`, `EMAIL_PASS`, and `ADMIN_EMAIL`
2. Make a booking
3. **Expected**: Both customer and admin get emails

### Test 3: Missing Config
1. Remove `EMAIL_USER` or `EMAIL_PASS`
2. Make a booking
3. **Expected**: Graceful skip, no errors

## Console Logs:

### ✅ **Properly Configured:**
```
[INFO] Basic email configuration available
[INFO] Admin email configuration available
[INFO] Booking confirmation email sent
[INFO] Admin booking notification email sent
```

### ⚠️ **Partially Configured:**
```
[WARN] Admin email not configured - admin notifications will be disabled
[INFO] Booking confirmation email sent
[WARN] Admin email sending skipped - ADMIN_EMAIL not configured
```

### ❌ **Not Configured:**
```
[WARN] Email configuration incomplete
[WARN] Email sending skipped - basic email configuration missing
```

## Benefits:

1. **No More Crashes**: Email errors won't break payment flow
2. **Partial Functionality**: Customer emails work even without admin config
3. **Clear Logging**: Easy to debug email issues
4. **Graceful Degradation**: System works with or without email

## Quick Fix:

अभी के लिए सिर्फ ये add करें:
```bash
ADMIN_EMAIL=<EMAIL>
```

यह आपके existing email configuration को complete कर देगा!
