import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/app/lib/mongodb";
import { verifyToken } from "@/app/lib/auth";
import { logger } from "@/app/config/logger";

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, message: "Authorization token required" },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json(
        { success: false, message: "Invalid or expired token" },
        { status: 401 }
      );
    }

    // Connect to database
    const { db } = await connectToDatabase();

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");
    const service = searchParams.get("service");
    const location = searchParams.get("location");

    // Build query for available technicians
    const query: any = {
      status: "active",
      isAvailable: true,
      $or: [
        { isOnline: true },
        { lastActive: { $gte: new Date(Date.now() - 30 * 60 * 1000) } } // Active in last 30 minutes
      ]
    };

    // Filter by service if provided
    if (service) {
      query.services = { $in: [service] };
    }

    // Get available technicians
    const technicians = await db
      .collection("technicians")
      .find(query)
      .limit(limit)
      .sort({ rating: -1, lastActive: -1 }) // Sort by rating and recent activity
      .toArray();

    // Format technician data
    const formattedTechnicians = technicians.map(tech => ({
      _id: tech._id,
      name: tech.name,
      phone: tech.phone,
      email: tech.email,
      rating: tech.rating || 4.5,
      completedJobs: tech.completedBookings || 0,
      services: tech.services || [],
      location: tech.location || "Unknown",
      isOnline: tech.isOnline || false,
      lastActive: tech.lastActive,
      profileImage: tech.profileImage || null,
      experience: tech.experience || "2+ years",
      specialization: tech.specialization || "General Repair"
    }));

    logger.info("Available technicians fetched", {
      userId: decoded.userId,
      count: formattedTechnicians.length,
      service,
      location
    });

    return NextResponse.json({
      success: true,
      technicians: formattedTechnicians,
      total: formattedTechnicians.length,
      message: `Found ${formattedTechnicians.length} available technicians`
    });

  } catch (error) {
    logger.error("Error fetching available technicians", {
      error: error instanceof Error ? error.message : "Unknown error"
    });
    
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST endpoint to update technician availability
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, message: "Authorization token required" },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json(
        { success: false, message: "Invalid or expired token" },
        { status: 401 }
      );
    }

    // Parse request body
    const { technicianId, isAvailable } = await request.json();

    if (!technicianId || typeof isAvailable !== 'boolean') {
      return NextResponse.json(
        { success: false, message: "Technician ID and availability status required" },
        { status: 400 }
      );
    }

    // Connect to database
    const { db } = await connectToDatabase();

    // Update technician availability
    const result = await db.collection("technicians").updateOne(
      { _id: technicianId },
      {
        $set: {
          isAvailable,
          lastActive: new Date(),
          updatedAt: new Date()
        }
      }
    );

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { success: false, message: "Technician not found" },
        { status: 404 }
      );
    }

    logger.info("Technician availability updated", {
      technicianId,
      isAvailable,
      updatedBy: decoded.userId
    });

    return NextResponse.json({
      success: true,
      message: `Technician availability updated to ${isAvailable ? 'available' : 'unavailable'}`,
      technicianId,
      isAvailable
    });

  } catch (error) {
    logger.error("Error updating technician availability", {
      error: error instanceof Error ? error.message : "Unknown error"
    });
    
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
